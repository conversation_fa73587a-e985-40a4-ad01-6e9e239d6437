# Copyright (c) 2020 Mobvoi Inc. (authors: <PERSON><PERSON>, <PERSON>)
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import argparse
import codecs
import copy
import random
import re
import numpy as np
import torch
import yaml
from PIL import Image
from PIL.Image import Resampling
from torch.nn.utils.rnn import pad_sequence
from torch.utils.data import Dataset, DataLoader

seed = 777
np.random.seed(seed)
random.seed(seed)

local_path = 'wenet/dataset/datasetfiles/'
IGNORE_ID = -1

wordsdict = {'<blank>': 0,
'<unk>': 1,
' ': 2,
'a': 3,
'b': 4,
'c': 5,
'd': 6,
'e': 7,
'f': 8,
'g': 9,
'h': 10,
'i': 11,
'j': 12,
'k': 13,
'l': 14,
'm': 15,
'n': 16,
'o': 17,
'p': 18,
'q': 19,
'r': 20,
's': 21,
't': 22,
'u': 23,
'v': 24,
'w': 25,
'x': 26,
'y': 27,
'z': 28,
'I': 29,
'<sos/eos>': 30}

# TODO: 添加归一化函数
# 伸缩
def _spec_augmentation(x,
                       warp_for_time=True,
                       max_w=80):
    """ Deep copy x and do spec augmentation then return it

    Args:
        x: input feature, T * F 2D
        num_t_mask: number of time mask to apply
        num_f_mask: number of freq mask to apply
        max_t: max width of time mask
        max_f: max width of freq mask
        max_w: max width of time warp

    Returns:
        augmented feature
    """
    y = np.copy(x)
    max_frames = y.shape[0]
    max_freq = y.shape[1]

    warped = round(random.randrange(0, round(max_frames/4)) - max_frames/8)
    y = Image.fromarray(x).resize((max_freq, max_frames+warped), Resampling.BICUBIC)
    y = np.array(y)
    # TODO: 幅度拉伸 （速度幅度变换）
    return y

def _spec_substitute(x, threshold=0.25, warp_for_time=True, max_t=20, num_t_sub=3):
    """ Deep copy x and do spec substitute then return it

    Args:
        x: input feature, T * F 2D
        max_t: max width of time substitute
        num_t_sub: number of time substitute to apply

    Returns:
        augmented feature
    """
    y = np.copy(x)
    max_frames = y.shape[0]
    start_index = 0
    end_index = max_frames-1
    arr = np.mean(y, axis=1)
    min_val = arr.min()
    max_val = arr.max()

    # 将数组映射到0到1之间
    mapped_arr = (arr - min_val) / (max_val - min_val)

    for i in range(max_frames):
        # print(np.mean(y[i, :]))
        if mapped_arr[i] < threshold:
            start_index = i
        else:
            break
    for i in range(max_frames):
        if mapped_arr[max_frames-1-i] < threshold:
            end_index = max_frames-1-i
        else:
            break
    
    start = random.randint(0, start_index)
    end = random.randint(end_index, max_frames-1)
    return y[start:end, :]


def _load_feature(batch, feat_dim):
    """ Load acoustic feature from files.

    The features have been prepared in previous step, usualy by Kaldi.

    Args:
        batch: a list of tuple (wav id , feature ark path).

    Returns:
        (keys, feats, labels)
    """
    keys = []
    feats = []
    lengths = []
    for i, x in enumerate(batch):
        try:
            data_path = local_path+x[1]
            mat = np.load(data_path)
            mat = mat[:,0:feat_dim]#这里可以进行剪头剪尾，对第0维度
            feats.append(mat)
            keys.append(x[0])
            lengths.append(mat.shape[0])
        except (Exception):
            # logging.warn('read utterance {} error'.format(x[0]))
            pass
    # Sort it because sorting is required in pack/pad operation
    order = np.argsort(lengths)[::-1]
    sorted_keys = [keys[i] for i in order]
    sorted_feats = [feats[i] for i in order]
        # 转换每个字符为其ASCII值的字符串表示
    encoded_batch = [[str(wordsdict[char]) for char in x[2]] for x in batch]
    # labels = np.array(labels)
    # encoded_batch = [[str(ord(char)) for char in x[2]] for x in batch]
    # # labels = [x[2].split() for x in batch]
    labels = [np.fromiter(map(int, x), dtype=np.int32) for x in encoded_batch]
    sorted_labels = [labels[i] for i in order]
    return sorted_keys, sorted_feats, sorted_labels

class CollateFunc(object):
    """ Collate function for AudioDataset
    """
    def __init__(self,
                 feature_dither=0.0,
                 spec_aug=False,
                 spec_aug_conf=None,
                 spec_sub=False,
                 spec_sub_conf=None,
                 feat_dim = 128,
                 ):
        """
        Args:
            raw_wav:
                    True if input is raw wav and feature extraction is needed.
                    False if input is extracted feature
        """
        # 特征增强配置
        self.spec_aug = spec_aug
        self.spec_aug_conf = spec_aug_conf

        self.spec_sub = spec_sub
        self.spec_sub_conf = spec_sub_conf

        self.feature_dither = feature_dither 

        self.feat_dim = feat_dim
    def __call__(self, batch):
        assert (len(batch) == 1)

        keys, xs, ys = _load_feature(batch[0], self.feat_dim)

        train_flag = True
        if ys is None:
            train_flag = False

        # optional feature dither d ~ (-a, a) on fbank feature
        # a ~ (0, 0.5)
        if self.feature_dither != 0.0:
            a = random.uniform(0, self.feature_dither)
            xs = [x + (np.random.random_sample(x.shape) - 0.5) * a for x in xs]

        # optinoal spec substitute
        if self.spec_sub:
            xs = [_spec_substitute(x, **self.spec_sub_conf) for x in xs]

        # optinoal spec augmentation
        if self.spec_aug:
            xs = [_spec_augmentation(x, **self.spec_aug_conf) for x in xs]

        # padding
        xs_lengths = torch.from_numpy(
            np.array([x.shape[0] for x in xs], dtype=np.int32))

        # pad_sequence will FAIL in case xs is empty
        if len(xs) > 0:
            # 所有的特征向量都被填充到了相同的长度
            xs_pad = pad_sequence([torch.from_numpy(x).float() for x in xs],
                                  True, 0)
        else:
            xs_pad = torch.Tensor(xs)

        if train_flag:
            ys_lengths = torch.from_numpy(
                np.array([y.shape[0] for y in ys], dtype=np.int32))
            if len(ys) > 0:
                ys_pad = pad_sequence([torch.from_numpy(y).int() for y in ys],
                                      True, IGNORE_ID)
            else:
                ys_pad = torch.Tensor(ys)
        else:
            ys_pad = None
            ys_lengths = None
        return keys, xs_pad, ys_pad, xs_lengths, ys_lengths


class AudioDataset(Dataset):
    def __init__(self,
                 data_file,
                 max_length=10240,
                 min_length=0,
                 batch_type='static',
                 batch_size=1,
                 max_frames_in_batch=0,
                 sort=True,
                 raw_wav=True):
        """Dataset for loading audio data.

        Attributes::
            data_file: input data file
                Plain text data file, each line contains following 7 fields,
                which is split by '\t':
                    utt:utt1
                    feat:tmp/data/file1.wav or feat:tmp/data/fbank.ark:30
                    feat_shape: 4.95(in seconds) or feat_shape:495,80(495 is in frames)
                    text:i love you
                    token: i <space> l o v e <space> y o u
                    tokenid: int id of this token
                    token_shape: M,N    # M is the number of token, N is vocab size
            max_length: drop utterance which is greater than max_length(ms)
            min_length: drop utterance which is less than min_length(ms)
            batch_type: static or dynamic, see max_frames_in_batch(dynamic)
            batch_size: number of utterances in a batch,
               it's for static batch size.
            max_frames_in_batch: max feature frames in a batch,
               when batch_type is dynamic, it's for dynamic batch size.
               Then batch_size is ignored, we will keep filling the
               batch until the total frames in batch up to max_frames_in_batch.
            sort: whether to sort all data, so the utterance with the same
               length could be filled in a same batch.
            raw_wav: use raw wave or extracted featute.
                if raw wave is used, dynamic waveform-level augmentation could be used
                and the feature is extracted by torchaudio.
                if extracted featute(e.g. by kaldi) is used, only feature-level
                augmentation such as specaug could be used.
        """
        assert batch_type in ['static', 'dynamic']
        data = []

        # Open in utf8 mode since meet encoding problem
        with codecs.open(data_file, 'r', encoding='utf-8') as f:
            
            for line in f:
                arr = re.split(r'\t| ', line.strip())
                # print(arr)
                # if len(arr) != 4:         # 2024/8/15
                if len(arr) < 4:
                    continue
                key = arr[0]
                feat_path = arr[1]
                num_frames = int(arr[2])
                # tokenid = arr[3]          # 2024/8/15
                tokenid = " ".join(arr[3])  # 将剩下的字段合并为一个字符串
                output_dim = len(wordsdict) #int(len(arr[3])), 应该是ASCII值对应最大的值
                data.append((key, feat_path, num_frames, tokenid))
                self.input_dim = 128 #feat_dim
                self.output_dim = output_dim
        if sort:
            data = sorted(data, key=lambda x: x[2])
        valid_data = []
        for i in range(len(data)):
            length = data[i][2]
            if length > max_length or length < min_length:
                # logging.warn('ignore utterance {} feature {}'.format(
                #     data[i][0], length))
                pass
            else:
                valid_data.append(data[i])
        data = valid_data
        self.minibatch = []
        num_data = len(data)
        # Dynamic batch size
        if batch_type == 'dynamic':
            assert (max_frames_in_batch > 0)
            self.minibatch.append([])
            num_frames_in_batch = 0
            for i in range(num_data):
                length = data[i][2]
                num_frames_in_batch += length
                if num_frames_in_batch > max_frames_in_batch:
                    self.minibatch.append([])
                    num_frames_in_batch = length
                self.minibatch[-1].append((data[i][0], data[i][1], data[i][3]))
        # Static batch size
        else:
            cur = 0
            while cur < num_data:
                end = min(cur + batch_size, num_data)
                item = []
                for i in range(cur, end):
                    item.append((data[i][0], data[i][1], data[i][3]))
                self.minibatch.append(item)
                cur = end

    def __len__(self):
        return len(self.minibatch)

    def __getitem__(self, idx):
        return self.minibatch[idx]
    

if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument('--config_file', help='config file', default='wenet/dataset/configfile.yaml')
    parser.add_argument('--data_file', help='input data file', default='wenet/dataset/datasetfiles/merged_test_file_logs.txt')
    args = parser.parse_args()

    with open(args.config_file, 'r') as fin:
        configs = yaml.load(fin, Loader=yaml.FullLoader)

    # Init dataset and data loader
    collate_conf = copy.copy(configs['collate_conf'])

    collate_func = CollateFunc(**collate_conf)
    dataset_conf = configs.get('dataset_conf', {})
    dataset = AudioDataset(args.data_file, **dataset_conf)

    data_loader = DataLoader(dataset,
                             batch_size=1,
                             shuffle=True,
                             sampler=None,
                             num_workers=0,
                             collate_fn=collate_func)

    for i, batch in enumerate(data_loader):
        print(i)
        print(batch[1].shape)
